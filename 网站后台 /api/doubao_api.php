<?php
/**
 * 豆包API接口
 * 提供豆包AI服务的独立API接口
 * 
 * @version 1.0.0
 */

// 防止直接访问
if (!defined('API_ACCESS')) {
    define('API_ACCESS', true);
}

// 加载必要的文件
require_once __DIR__ . '/api_base.php';

class DoubaoApi extends ApiBase {
    private $doubaoApiUrl = 'https://ark.cn-beijing.volces.com/api/v3';
    private $supportedModels = [
        'doubao-seed-1-6-250615' => [
            'id' => 'doubao-seed-1-6-250615',
            'name' => '豆包 Seed 1.6',
            'description' => '豆包基础对话模型',
            'max_tokens' => 4096,
            'supports_streaming' => true,
            'supports_vision' => false
        ],
        'doubao-1.5-vision-pro-250328' => [
            'id' => 'doubao-1.5-vision-pro-250328',
            'name' => '豆包 1.5 Vision Pro',
            'description' => '豆包视觉理解模型，支持图像分析',
            'max_tokens' => 4096,
            'supports_streaming' => true,
            'supports_vision' => true
        ],
        'doubao-seed-1-6-thinking-250715' => [
            'id' => 'doubao-seed-1-6-thinking-250715',
            'name' => '豆包 Seed 1.6 Thinking',
            'description' => '豆包思考模型，支持深度推理',
            'max_tokens' => 8192,
            'supports_streaming' => true,
            'supports_thinking' => true
        ]
    ];

    /**
     * 处理请求
     */
    public function handleRequest() {
        // 解析请求路径
        $path = $_SERVER['PATH_INFO'] ?? '';
        $path = trim($path, '/');
        
        // 分割路径获取端点
        $segments = explode('/', $path);
        $endpoint = $segments[1] ?? ''; // segments[0] 是 'doubao'
        
        // 路由处理
        switch ($endpoint) {
            case 'models':
                return $this->handleModels();
            case 'chat':
                return $this->handleChat();
            case 'validate':
                return $this->handleValidate();
            case 'status':
                return $this->handleStatus();
            default:
                return $this->respondError('Invalid endpoint', 404);
        }
    }

    /**
     * 获取支持的模型列表
     */
    private function handleModels() {
        if ($this->method !== 'GET') {
            return $this->respondError('Method not allowed', 405);
        }

        $models = array_values($this->supportedModels);
        
        return $this->respondSuccess([
            'models' => $models,
            'total' => count($models)
        ], 'Models retrieved successfully');
    }

    /**
     * 处理聊天完成请求
     */
    private function handleChat() {
        if ($this->method !== 'POST') {
            return $this->respondError('Method not allowed', 405);
        }

        // 验证API密钥
        $apiKey = $this->getApiKeyFromHeader();
        if (!$apiKey) {
            return $this->respondError('Missing API key', 401);
        }

        // 获取请求参数
        $model = $this->request['model'] ?? 'doubao-seed-1-6-250615';
        $messages = $this->request['messages'] ?? [];
        $maxTokens = $this->request['max_tokens'] ?? 4096;
        $temperature = $this->request['temperature'] ?? 0.7;
        $stream = $this->request['stream'] ?? false;
        $enableThinking = $this->request['enable_thinking'] ?? false;

        // 验证参数
        if (empty($messages) || !is_array($messages)) {
            return $this->respondError('Invalid messages parameter', 400);
        }

        if (!isset($this->supportedModels[$model])) {
            return $this->respondError('Unsupported model', 400);
        }

        // 验证API密钥有效性
        $keyValidation = $this->validateApiKey($apiKey);
        if (!$keyValidation['valid']) {
            return $this->respondError('Invalid API key: ' . $keyValidation['error'], 401);
        }

        // 构建请求数据
        $requestData = [
            'model' => $model,
            'messages' => $messages,
            'max_tokens' => min($maxTokens, $this->supportedModels[$model]['max_tokens']),
            'temperature' => max(0, min(2, $temperature)),
            'stream' => $stream
        ];

        // 如果是思考模型且启用了思考功能
        if ($model === 'doubao-seed-1-6-thinking-250715' && $enableThinking) {
            $requestData['thinking'] = true;
        }

        try {
            // 发送请求到豆包API
            $response = $this->sendDoubaoRequest('/chat/completions', $requestData, $apiKey);
            
            if ($response['success']) {
                // 记录API使用日志
                $this->logApiUsage('doubao', 'chat', $model, $apiKey);
                
                return $this->respondSuccess($response['data'], 'Chat completion successful');
            } else {
                return $this->respondError($response['error'], $response['code'] ?? 500);
            }
            
        } catch (Exception $e) {
            error_log("豆包API请求失败: " . $e->getMessage());
            return $this->respondError('API request failed: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 验证API密钥
     */
    private function handleValidate() {
        if ($this->method !== 'POST') {
            return $this->respondError('Method not allowed', 405);
        }

        $apiKey = $this->request['api_key'] ?? '';
        
        if (empty($apiKey)) {
            return $this->respondError('Missing API key', 400);
        }

        $validation = $this->validateApiKey($apiKey);
        
        if ($validation['valid']) {
            return $this->respondSuccess([
                'valid' => true,
                'models' => array_keys($this->supportedModels)
            ], 'API key is valid');
        } else {
            return $this->respondError($validation['error'], 401);
        }
    }

    /**
     * 获取API状态
     */
    private function handleStatus() {
        if ($this->method !== 'GET') {
            return $this->respondError('Method not allowed', 405);
        }

        try {
            // 检查豆包API状态
            $statusCheck = $this->checkDoubaoStatus();
            
            return $this->respondSuccess([
                'service' => 'doubao',
                'status' => $statusCheck['status'],
                'response_time' => $statusCheck['response_time'],
                'models_available' => count($this->supportedModels),
                'last_check' => date('Y-m-d H:i:s')
            ], 'Status retrieved successfully');
            
        } catch (Exception $e) {
            return $this->respondError('Status check failed: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 从请求头获取API密钥
     */
    private function getApiKeyFromHeader() {
        $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
        
        if (preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
            return $matches[1];
        }
        
        // 也支持从X-API-Key头获取
        return $_SERVER['HTTP_X_API_KEY'] ?? '';
    }

    /**
     * 验证豆包API密钥
     */
    public function validateApiKey($apiKey) {
        if (!$apiKey || strlen($apiKey) < 32) {
            return [
                'valid' => false,
                'error' => 'Invalid API key format'
            ];
        }

        try {
            // 使用简单的聊天请求验证API密钥
            $testData = [
                'model' => 'doubao-seed-1-6-250615',
                'messages' => [
                    ['role' => 'user', 'content' => 'Hello']
                ],
                'max_tokens' => 10
            ];
            
            $response = $this->sendDoubaoRequest('/chat/completions', $testData, $apiKey);
            
            if ($response['success']) {
                return ['valid' => true];
            } else {
                return [
                    'valid' => false,
                    'error' => $response['error'] ?? 'API key validation failed'
                ];
            }
            
        } catch (Exception $e) {
            return [
                'valid' => false,
                'error' => 'Validation request failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 发送请求到豆包API
     */
    private function sendDoubaoRequest($endpoint, $data = [], $apiKey = '', $method = 'POST') {
        $url = $this->doubaoApiUrl . $endpoint;
        
        $headers = [
            'Content-Type: application/json',
            'User-Agent: XiaoMeiHua-Backend/1.0'
        ];
        
        if ($apiKey) {
            $headers[] = 'Authorization: Bearer ' . $apiKey;
        }

        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_CONNECTTIMEOUT => 10,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_SSL_VERIFYPEER => true,
            CURLOPT_SSL_VERIFYHOST => 2,
            CURLOPT_FOLLOWLOCATION => false,
            CURLOPT_MAXREDIRS => 0
        ]);

        if ($method === 'POST') {
            curl_setopt($ch, CURLOPT_POST, true);
            if (!empty($data)) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            }
        }

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            throw new Exception("cURL error: $error");
        }

        $responseData = json_decode($response, true);
        
        if ($httpCode >= 200 && $httpCode < 300) {
            return [
                'success' => true,
                'data' => $responseData
            ];
        } else {
            return [
                'success' => false,
                'code' => $httpCode,
                'error' => $responseData['error']['message'] ?? 'Unknown error',
                'data' => $responseData
            ];
        }
    }

    /**
     * 检查豆包API状态
     */
    public function checkDoubaoStatus() {
        $startTime = microtime(true);
        
        try {
            // 使用简单的请求检查状态
            $testData = [
                'model' => 'doubao-seed-1-6-250615',
                'messages' => [['role' => 'user', 'content' => 'ping']],
                'max_tokens' => 1
            ];
            
            $response = $this->sendDoubaoRequest('/chat/completions', $testData, 'test');
            $endTime = microtime(true);
            
            // 即使API密钥无效，只要服务在线就返回online
            return [
                'status' => 'online',
                'response_time' => round(($endTime - $startTime) * 1000, 2) // ms
            ];
            
        } catch (Exception $e) {
            $endTime = microtime(true);
            return [
                'status' => 'offline',
                'response_time' => round(($endTime - $startTime) * 1000, 2),
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 记录API使用日志
     */
    private function logApiUsage($service, $action, $model, $apiKey) {
        try {
            $maskedKey = substr($apiKey, 0, 8) . '...';
            
            $stmt = $this->db->prepare("
                INSERT INTO ai_api_usage_logs 
                (service, action, model, api_key_masked, ip_address, user_agent, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, NOW())
            ");
            
            $stmt->execute([
                $service,
                $action,
                $model,
                $maskedKey,
                $_SERVER['REMOTE_ADDR'] ?? '',
                $_SERVER['HTTP_USER_AGENT'] ?? ''
            ]);
            
        } catch (PDOException $e) {
            // 日志记录失败不影响API响应
            error_log("AI API使用日志记录失败: " . $e->getMessage());
        }
    }
}

// 如果直接访问此文件，处理请求
if (basename($_SERVER['SCRIPT_NAME']) === 'doubao_api.php') {
    $api = new DoubaoApi();
    $api->handleRequest();
}
