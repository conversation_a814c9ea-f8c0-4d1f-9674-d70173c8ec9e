<?php
/**
 * 简化的DeepSeek API接口
 * 不依赖复杂的基础类，直接处理请求
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置响应头
header('Content-Type: application/json; charset=UTF-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-API-Key');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

class SimpleDeepSeekApi {
    private $deepseekApiUrl = 'https://api.deepseek.com';
    private $supportedModels = [
        'deepseek-chat' => [
            'id' => 'deepseek-chat',
            'name' => 'DeepSeek Chat',
            'description' => 'DeepSeek通用对话模型',
            'max_tokens' => 4096,
            'supports_streaming' => true
        ],
        'deepseek-reasoner' => [
            'id' => 'deepseek-reasoner',
            'name' => 'DeepSeek-R1-0528',
            'description' => 'DeepSeek推理模型，支持深度思考',
            'max_tokens' => 8192,
            'supports_streaming' => true,
            'supports_reasoning' => true
        ]
    ];

    public function handleRequest() {
        $method = $_SERVER['REQUEST_METHOD'];
        $path = $_SERVER['PATH_INFO'] ?? $_SERVER['REQUEST_URI'] ?? '';
        
        // 解析路径
        $segments = explode('/', trim($path, '/'));
        $endpoint = end($segments);
        
        try {
            switch ($endpoint) {
                case 'models':
                    return $this->handleModels($method);
                case 'chat':
                    return $this->handleChat($method);
                case 'validate':
                    return $this->handleValidate($method);
                case 'status':
                    return $this->handleStatus($method);
                default:
                    return $this->respondError('Invalid endpoint: ' . $endpoint, 404);
            }
        } catch (Exception $e) {
            return $this->respondError('Server error: ' . $e->getMessage(), 500);
        }
    }

    private function handleModels($method) {
        if ($method !== 'GET') {
            return $this->respondError('Method not allowed', 405);
        }

        return $this->respondSuccess([
            'models' => array_values($this->supportedModels),
            'total' => count($this->supportedModels)
        ], 'Models retrieved successfully');
    }

    private function handleChat($method) {
        if ($method !== 'POST') {
            return $this->respondError('Method not allowed', 405);
        }

        // 获取API密钥
        $apiKey = $this->getApiKey();
        if (!$apiKey) {
            return $this->respondError('Missing API key', 401);
        }

        // 获取请求数据
        $requestData = $this->getRequestData();
        if (!$requestData) {
            return $this->respondError('Invalid request data', 400);
        }

        $model = $requestData['model'] ?? 'deepseek-chat';
        $messages = $requestData['messages'] ?? [];

        if (empty($messages)) {
            return $this->respondError('Messages are required', 400);
        }

        if (!isset($this->supportedModels[$model])) {
            return $this->respondError('Unsupported model', 400);
        }

        // 发送请求到DeepSeek
        $response = $this->sendToDeepSeek('/chat/completions', $requestData, $apiKey);
        
        if ($response['success']) {
            return $this->respondSuccess($response['data'], 'Chat completion successful');
        } else {
            return $this->respondError($response['error'], $response['code'] ?? 500);
        }
    }

    private function handleValidate($method) {
        if ($method !== 'POST') {
            return $this->respondError('Method not allowed', 405);
        }

        $requestData = $this->getRequestData();
        $apiKey = $requestData['api_key'] ?? '';

        if (empty($apiKey)) {
            return $this->respondError('API key is required', 400);
        }

        $validation = $this->validateApiKey($apiKey);
        
        if ($validation['valid']) {
            return $this->respondSuccess([
                'valid' => true,
                'models' => array_keys($this->supportedModels)
            ], 'API key is valid');
        } else {
            return $this->respondError($validation['error'], 401);
        }
    }

    private function handleStatus($method) {
        if ($method !== 'GET') {
            return $this->respondError('Method not allowed', 405);
        }

        $status = $this->checkStatus();
        
        return $this->respondSuccess([
            'service' => 'deepseek',
            'status' => $status['status'],
            'response_time' => $status['response_time'],
            'models_available' => count($this->supportedModels),
            'last_check' => date('Y-m-d H:i:s')
        ], 'Status retrieved successfully');
    }

    private function getApiKey() {
        $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
        
        if (preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
            return $matches[1];
        }
        
        return $_SERVER['HTTP_X_API_KEY'] ?? '';
    }

    private function getRequestData() {
        $input = file_get_contents('php://input');
        if (empty($input)) {
            return null;
        }

        $data = json_decode($input, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return null;
        }

        return $data;
    }

    private function validateApiKey($apiKey) {
        if (!$apiKey || strlen($apiKey) < 10) {
            return [
                'valid' => false,
                'error' => 'Invalid API key format'
            ];
        }

        try {
            $testData = [
                'model' => 'deepseek-chat',
                'messages' => [['role' => 'user', 'content' => 'Hello']],
                'max_tokens' => 10
            ];
            
            $response = $this->sendToDeepSeek('/chat/completions', $testData, $apiKey);
            
            return [
                'valid' => $response['success'],
                'error' => $response['success'] ? null : $response['error']
            ];
            
        } catch (Exception $e) {
            return [
                'valid' => false,
                'error' => 'Validation failed: ' . $e->getMessage()
            ];
        }
    }

    private function checkStatus() {
        $startTime = microtime(true);
        
        try {
            $ch = curl_init();
            curl_setopt_array($ch, [
                CURLOPT_URL => $this->deepseekApiUrl,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_TIMEOUT => 10,
                CURLOPT_CONNECTTIMEOUT => 5,
                CURLOPT_NOBODY => true,
                CURLOPT_SSL_VERIFYPEER => false
            ]);

            $result = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            $endTime = microtime(true);
            
            return [
                'status' => ($httpCode >= 200 && $httpCode < 500) ? 'online' : 'offline',
                'response_time' => round(($endTime - $startTime) * 1000, 2)
            ];
            
        } catch (Exception $e) {
            $endTime = microtime(true);
            return [
                'status' => 'offline',
                'response_time' => round(($endTime - $startTime) * 1000, 2),
                'error' => $e->getMessage()
            ];
        }
    }

    private function sendToDeepSeek($endpoint, $data, $apiKey) {
        $url = $this->deepseekApiUrl . $endpoint;
        
        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $apiKey,
            'User-Agent: XiaoMeiHua-Backend/1.0'
        ];

        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_CONNECTTIMEOUT => 10,
            CURLOPT_SSL_VERIFYPEER => false
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            return [
                'success' => false,
                'error' => 'cURL error: ' . $error
            ];
        }

        $responseData = json_decode($response, true);
        
        if ($httpCode >= 200 && $httpCode < 300) {
            return [
                'success' => true,
                'data' => $responseData ?: []
            ];
        } else {
            $errorMessage = 'Unknown error';
            if ($responseData && isset($responseData['error'])) {
                $errorMessage = is_array($responseData['error']) 
                    ? ($responseData['error']['message'] ?? 'API error')
                    : $responseData['error'];
            }
            
            return [
                'success' => false,
                'code' => $httpCode,
                'error' => $errorMessage
            ];
        }
    }

    private function respondSuccess($data = [], $message = 'Success') {
        http_response_code(200);
        echo json_encode([
            'success' => true,
            'message' => $message,
            'data' => $data
        ]);
        exit;
    }

    private function respondError($message = 'Error', $code = 400) {
        http_response_code($code);
        echo json_encode([
            'success' => false,
            'message' => $message
        ]);
        exit;
    }
}

// 处理请求
$api = new SimpleDeepSeekApi();
$api->handleRequest();
?>
