<?php
/**
 * API接口简单测试脚本
 * 用于验证DeepSeek和豆包API是否正常工作
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置响应头
header('Content-Type: application/json; charset=UTF-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 获取测试类型
$test = $_GET['test'] ?? 'status';

try {
    switch ($test) {
        case 'deepseek_models':
            testDeepSeekModels();
            break;
        case 'deepseek_status':
            testDeepSeekStatus();
            break;
        case 'doubao_models':
            testDoubaoModels();
            break;
        case 'doubao_status':
            testDoubaoStatus();
            break;
        case 'config':
            testConfig();
            break;
        default:
            echo json_encode([
                'success' => true,
                'message' => 'API测试脚本运行正常',
                'available_tests' => [
                    'deepseek_models' => '测试DeepSeek模型列表',
                    'deepseek_status' => '测试DeepSeek状态',
                    'doubao_models' => '测试豆包模型列表',
                    'doubao_status' => '测试豆包状态',
                    'config' => '测试配置加载'
                ],
                'usage' => '在URL后添加?test=测试名称'
            ]);
    }
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ]);
}

function testDeepSeekModels() {
    define('API_ACCESS', true);
    require_once __DIR__ . '/deepseek_api.php';
    
    // 模拟请求
    $_SERVER['PATH_INFO'] = '/deepseek/models';
    $_SERVER['REQUEST_METHOD'] = 'GET';
    
    $api = new DeepSeekApi();
    $api->handleRequest();
}

function testDeepSeekStatus() {
    define('API_ACCESS', true);
    require_once __DIR__ . '/deepseek_api.php';
    
    // 模拟请求
    $_SERVER['PATH_INFO'] = '/deepseek/status';
    $_SERVER['REQUEST_METHOD'] = 'GET';
    
    $api = new DeepSeekApi();
    $api->handleRequest();
}

function testDoubaoModels() {
    define('API_ACCESS', true);
    require_once __DIR__ . '/doubao_api.php';
    
    // 模拟请求
    $_SERVER['PATH_INFO'] = '/doubao/models';
    $_SERVER['REQUEST_METHOD'] = 'GET';
    
    $api = new DoubaoApi();
    $api->handleRequest();
}

function testDoubaoStatus() {
    define('API_ACCESS', true);
    require_once __DIR__ . '/doubao_api.php';
    
    // 模拟请求
    $_SERVER['PATH_INFO'] = '/doubao/status';
    $_SERVER['REQUEST_METHOD'] = 'GET';
    
    $api = new DoubaoApi();
    $api->handleRequest();
}

function testConfig() {
    define('API_ACCESS', true);
    require_once __DIR__ . '/config.php';
    
    try {
        $config = new ApiConfig();
        echo json_encode([
            'success' => true,
            'message' => '配置加载成功',
            'config' => [
                'api_version' => $config->get('api_version'),
                'debug_mode' => $config->isDebugMode(),
                'database_config' => [
                    'host' => $config->getDatabaseConfig()['host'],
                    'dbname' => $config->getDatabaseConfig()['dbname']
                ]
            ]
        ]);
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'error' => '配置加载失败: ' . $e->getMessage()
        ]);
    }
}
?>
