-- AI服务相关数据库表
-- 创建时间: 2025-01-17

-- AI服务配置表
CREATE TABLE IF NOT EXISTS `ai_service_configs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `service` varchar(50) NOT NULL COMMENT 'AI服务名称 (deepseek, doubao)',
  `user_id` int(11) NOT NULL DEFAULT 1 COMMENT '用户ID',
  `enabled` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否启用',
  `api_keys` text COMMENT 'API密钥列表 (JSON格式)',
  `model` varchar(100) DEFAULT NULL COMMENT '当前使用的模型',
  `settings` text COMMENT '其他设置 (JSON格式)',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_service_user` (`service`, `user_id`),
  KEY `idx_service` (`service`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_enabled` (`enabled`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI服务配置表';

-- AI API使用日志表
CREATE TABLE IF NOT EXISTS `ai_api_usage_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `service` varchar(50) NOT NULL COMMENT 'AI服务名称',
  `action` varchar(50) NOT NULL COMMENT '操作类型 (chat, validate, status)',
  `model` varchar(100) DEFAULT NULL COMMENT '使用的模型',
  `api_key_masked` varchar(20) DEFAULT NULL COMMENT '掩码后的API密钥',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text COMMENT '用户代理',
  `request_data` text COMMENT '请求数据 (JSON格式)',
  `response_status` varchar(20) DEFAULT 'success' COMMENT '响应状态',
  `response_time` int(11) DEFAULT NULL COMMENT '响应时间(毫秒)',
  `error_message` text COMMENT '错误信息',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_service` (`service`),
  KEY `idx_action` (`action`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_response_status` (`response_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI API使用日志表';

-- AI服务状态监控表
CREATE TABLE IF NOT EXISTS `ai_service_status` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `service` varchar(50) NOT NULL COMMENT 'AI服务名称',
  `status` varchar(20) NOT NULL COMMENT '服务状态 (online, offline, error)',
  `response_time` int(11) DEFAULT NULL COMMENT '响应时间(毫秒)',
  `error_message` text COMMENT '错误信息',
  `check_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_service` (`service`),
  KEY `idx_status` (`status`),
  KEY `idx_check_time` (`check_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI服务状态监控表';

-- AI对话历史表
CREATE TABLE IF NOT EXISTS `ai_chat_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `service` varchar(50) NOT NULL COMMENT 'AI服务名称',
  `model` varchar(100) NOT NULL COMMENT '使用的模型',
  `session_id` varchar(100) DEFAULT NULL COMMENT '会话ID',
  `user_message` text NOT NULL COMMENT '用户消息',
  `ai_response` text COMMENT 'AI回复',
  `tokens_used` int(11) DEFAULT NULL COMMENT '使用的token数量',
  `response_time` int(11) DEFAULT NULL COMMENT '响应时间(毫秒)',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_service` (`service`),
  KEY `idx_model` (`model`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI对话历史表';

-- 插入默认配置数据
INSERT IGNORE INTO `ai_service_configs` (`service`, `user_id`, `enabled`, `api_keys`, `model`, `settings`) VALUES
('deepseek', 1, 0, '[]', 'deepseek-chat', '{"temperature": 0.7, "max_tokens": 4096, "enable_reasoning": false}'),
('doubao', 1, 0, '[]', 'doubao-seed-1-6-250615', '{"temperature": 0.7, "max_tokens": 4096, "enable_thinking": false}');

-- 创建索引优化查询性能
CREATE INDEX IF NOT EXISTS `idx_ai_usage_service_time` ON `ai_api_usage_logs` (`service`, `created_at`);
CREATE INDEX IF NOT EXISTS `idx_ai_status_service_time` ON `ai_service_status` (`service`, `check_time`);
CREATE INDEX IF NOT EXISTS `idx_ai_chat_service_time` ON `ai_chat_history` (`service`, `created_at`);

-- 创建视图方便查询
CREATE OR REPLACE VIEW `v_ai_service_summary` AS
SELECT 
    c.service,
    c.enabled,
    c.model,
    JSON_LENGTH(c.api_keys) as api_key_count,
    c.updated_at as config_updated,
    s.status as last_status,
    s.response_time as last_response_time,
    s.check_time as last_check_time,
    (SELECT COUNT(*) FROM ai_api_usage_logs WHERE service = c.service AND DATE(created_at) = CURDATE()) as today_usage_count
FROM ai_service_configs c
LEFT JOIN (
    SELECT service, status, response_time, check_time,
           ROW_NUMBER() OVER (PARTITION BY service ORDER BY check_time DESC) as rn
    FROM ai_service_status
) s ON c.service = s.service AND s.rn = 1;

-- 创建存储过程用于清理旧日志
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS `CleanupAiLogs`(IN days_to_keep INT)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- 清理API使用日志
    DELETE FROM ai_api_usage_logs 
    WHERE created_at < DATE_SUB(NOW(), INTERVAL days_to_keep DAY);
    
    -- 清理状态监控日志
    DELETE FROM ai_service_status 
    WHERE check_time < DATE_SUB(NOW(), INTERVAL days_to_keep DAY);
    
    -- 清理对话历史
    DELETE FROM ai_chat_history 
    WHERE created_at < DATE_SUB(NOW(), INTERVAL days_to_keep DAY);
    
    COMMIT;
END //
DELIMITER ;

-- 创建事件调度器自动清理日志（保留30天）
-- CREATE EVENT IF NOT EXISTS `auto_cleanup_ai_logs`
-- ON SCHEDULE EVERY 1 DAY
-- STARTS CURRENT_TIMESTAMP
-- DO CALL CleanupAiLogs(30);

-- 注释：如需启用自动清理，请取消上面的注释并确保事件调度器已启用
-- SET GLOBAL event_scheduler = ON;
