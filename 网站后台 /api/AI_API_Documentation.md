# AI服务API接口文档

## 概述

本文档描述了网站后台提供的DeepSeek和豆包AI服务的独立API接口。这些接口允许外部应用程序通过HTTP请求使用AI服务功能。

## 基础信息

- **基础URL**: `https://your-domain.com/api/gateway.php`
- **API版本**: v1.0
- **支持格式**: JSON
- **字符编码**: UTF-8

## 认证方式

### API密钥认证
```http
Authorization: Bearer YOUR_API_KEY
```

### 或使用自定义头
```http
X-API-Key: YOUR_API_KEY
```

## 支持的AI服务

### 1. DeepSeek
- **服务标识**: `deepseek`
- **API端点**: `/deepseek/*`
- **支持模型**:
  - `deepseek-chat`: 通用对话模型
  - `deepseek-reasoner`: 推理模型，支持深度思考

### 2. 豆包
- **服务标识**: `doubao`
- **API端点**: `/doubao/*`
- **支持模型**:
  - `doubao-seed-1-6-250615`: 基础对话模型
  - `doubao-1.5-vision-pro-250328`: 视觉理解模型
  - `doubao-seed-1-6-thinking-250715`: 思考模型

## API接口

### 1. 获取模型列表

#### DeepSeek模型列表
```http
GET /deepseek/models
```

#### 豆包模型列表
```http
GET /doubao/models
```

**响应示例**:
```json
{
  "success": true,
  "message": "Models retrieved successfully",
  "data": {
    "models": [
      {
        "id": "deepseek-chat",
        "name": "DeepSeek Chat",
        "description": "DeepSeek通用对话模型",
        "max_tokens": 4096,
        "supports_streaming": true
      }
    ],
    "total": 1
  }
}
```

### 2. 聊天完成

#### DeepSeek聊天
```http
POST /deepseek/chat
Authorization: Bearer YOUR_DEEPSEEK_API_KEY
Content-Type: application/json

{
  "model": "deepseek-chat",
  "messages": [
    {
      "role": "user",
      "content": "你好，请介绍一下自己"
    }
  ],
  "max_tokens": 4096,
  "temperature": 0.7,
  "stream": false,
  "enable_reasoning": false
}
```

#### 豆包聊天
```http
POST /doubao/chat
Authorization: Bearer YOUR_DOUBAO_API_KEY
Content-Type: application/json

{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": "你好，请介绍一下自己"
    }
  ],
  "max_tokens": 4096,
  "temperature": 0.7,
  "stream": false,
  "enable_thinking": false
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "Chat completion successful",
  "data": {
    "id": "chatcmpl-xxx",
    "object": "chat.completion",
    "created": 1705123456,
    "model": "deepseek-chat",
    "choices": [
      {
        "index": 0,
        "message": {
          "role": "assistant",
          "content": "你好！我是DeepSeek AI助手..."
        },
        "finish_reason": "stop"
      }
    ],
    "usage": {
      "prompt_tokens": 10,
      "completion_tokens": 20,
      "total_tokens": 30
    }
  }
}
```

### 3. API密钥验证

#### DeepSeek密钥验证
```http
POST /deepseek/validate
Content-Type: application/json

{
  "api_key": "sk-xxxxxxxxxxxxxxxx"
}
```

#### 豆包密钥验证
```http
POST /doubao/validate
Content-Type: application/json

{
  "api_key": "your-doubao-api-key"
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "API key is valid",
  "data": {
    "valid": true,
    "models": ["deepseek-chat", "deepseek-reasoner"]
  }
}
```

### 4. 服务状态检查

#### DeepSeek状态
```http
GET /deepseek/status
```

#### 豆包状态
```http
GET /doubao/status
```

**响应示例**:
```json
{
  "success": true,
  "message": "Status retrieved successfully",
  "data": {
    "service": "deepseek",
    "status": "online",
    "response_time": 245.67,
    "models_available": 2,
    "last_check": "2025-01-17 10:30:00"
  }
}
```

### 5. 统一AI服务管理

#### 获取支持的服务列表
```http
GET /ai/services
```

#### 获取所有服务状态
```http
GET /ai/status
```

#### 统一聊天接口
```http
POST /ai/chat
Content-Type: application/json

{
  "service": "deepseek",
  "model": "deepseek-chat",
  "messages": [
    {
      "role": "user",
      "content": "Hello"
    }
  ]
}
```

## 错误处理

### 错误响应格式
```json
{
  "success": false,
  "message": "Error description",
  "error_code": "ERROR_CODE"
}
```

### 常见错误码

| 错误码 | HTTP状态码 | 描述 |
|--------|------------|------|
| INVALID_API_KEY | 401 | API密钥无效 |
| MISSING_API_KEY | 401 | 缺少API密钥 |
| UNSUPPORTED_MODEL | 400 | 不支持的模型 |
| INVALID_PARAMETERS | 400 | 参数无效 |
| RATE_LIMIT_EXCEEDED | 429 | 请求频率超限 |
| SERVICE_UNAVAILABLE | 503 | 服务不可用 |

## 使用限制

- **请求频率**: 每分钟最多60次请求
- **请求大小**: 最大10MB
- **超时时间**: 30秒
- **并发连接**: 最多10个

## 示例代码

### JavaScript (Node.js)
```javascript
const axios = require('axios');

async function chatWithDeepSeek(message) {
  try {
    const response = await axios.post('https://your-domain.com/api/gateway.php/deepseek/chat', {
      model: 'deepseek-chat',
      messages: [{ role: 'user', content: message }],
      max_tokens: 4096,
      temperature: 0.7
    }, {
      headers: {
        'Authorization': 'Bearer YOUR_DEEPSEEK_API_KEY',
        'Content-Type': 'application/json'
      }
    });
    
    return response.data;
  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
  }
}
```

### Python
```python
import requests

def chat_with_doubao(message):
    url = 'https://your-domain.com/api/gateway.php/doubao/chat'
    headers = {
        'Authorization': 'Bearer YOUR_DOUBAO_API_KEY',
        'Content-Type': 'application/json'
    }
    data = {
        'model': 'doubao-seed-1-6-250615',
        'messages': [{'role': 'user', 'content': message}],
        'max_tokens': 4096,
        'temperature': 0.7
    }
    
    try:
        response = requests.post(url, json=data, headers=headers)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f'Error: {e}')
```

### cURL
```bash
# DeepSeek聊天
curl -X POST "https://your-domain.com/api/gateway.php/deepseek/chat" \
  -H "Authorization: Bearer YOUR_DEEPSEEK_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "deepseek-chat",
    "messages": [{"role": "user", "content": "Hello"}],
    "max_tokens": 4096,
    "temperature": 0.7
  }'

# 豆包聊天
curl -X POST "https://your-domain.com/api/gateway.php/doubao/chat" \
  -H "Authorization: Bearer YOUR_DOUBAO_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "doubao-seed-1-6-250615",
    "messages": [{"role": "user", "content": "你好"}],
    "max_tokens": 4096,
    "temperature": 0.7
  }'
```

## 更新日志

### v1.0 (2025-01-17)
- 初始版本发布
- 支持DeepSeek和豆包AI服务
- 提供聊天、验证、状态检查等基础功能
- 统一的API网关和错误处理
