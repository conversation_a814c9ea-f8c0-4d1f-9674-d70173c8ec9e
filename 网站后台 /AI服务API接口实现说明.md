# AI服务API接口实现说明

## 项目概述

已成功为网站后台开发了DeepSeek和豆包的独立API接口，通过这些接口可以使用DeepSeek和豆包的AI功能。

## 实现内容

### 1. 核心API接口

#### DeepSeek API接口 (`/api/deepseek_api.php`)
- ✅ **聊天完成**: `/deepseek/chat` - 支持DeepSeek Chat和DeepSeek-R1推理模型
- ✅ **模型列表**: `/deepseek/models` - 获取支持的模型列表
- ✅ **密钥验证**: `/deepseek/validate` - 验证DeepSeek API密钥有效性
- ✅ **服务状态**: `/deepseek/status` - 检查DeepSeek服务状态

#### 豆包API接口 (`/api/doubao_api.php`)
- ✅ **聊天完成**: `/doubao/chat` - 支持豆包全系列模型
- ✅ **模型列表**: `/doubao/models` - 获取支持的模型列表
- ✅ **密钥验证**: `/doubao/validate` - 验证豆包API密钥有效性
- ✅ **服务状态**: `/doubao/status` - 检查豆包服务状态

#### 统一AI服务管理接口 (`/api/ai_service_api.php`)
- ✅ **服务列表**: `/ai/services` - 获取所有支持的AI服务
- ✅ **配置管理**: `/ai/config` - 管理AI服务配置
- ✅ **状态监控**: `/ai/status` - 获取所有服务状态
- ✅ **统一聊天**: `/ai/chat` - 统一的聊天接口

### 2. API网关集成

#### 更新的网关路由 (`/api/gateway.php`)
```php
// 新增的AI服务路由
'ai' => [
    'handler' => 'handleAiService',
    'requireAuth' => false
],
'deepseek' => [
    'handler' => 'handleDeepSeek', 
    'requireAuth' => false
],
'doubao' => [
    'handler' => 'handleDoubao',
    'requireAuth' => false
]
```

### 3. 数据库支持

#### 数据库表结构 (`/sql/ai_services_tables.sql`)
- ✅ **ai_service_configs**: AI服务配置表
- ✅ **ai_api_usage_logs**: API使用日志表
- ✅ **ai_service_status**: 服务状态监控表
- ✅ **ai_chat_history**: 对话历史表

#### 安装脚本 (`/api/install_ai_services.php`)
- ✅ 自动创建所需数据库表
- ✅ 插入默认配置数据
- ✅ 表存在性检查功能

### 4. 文档和测试

#### API文档 (`/api/AI_API_Documentation.md`)
- ✅ 完整的接口说明
- ✅ 请求/响应示例
- ✅ 错误处理说明
- ✅ 多语言使用示例

#### 测试页面 (`/api/ai_api_test.html`)
- ✅ 可视化API测试界面
- ✅ 支持所有API端点测试
- ✅ 实时响应显示
- ✅ 响应式设计

## 支持的模型

### DeepSeek模型
| 模型ID | 名称 | 描述 | 特性 |
|--------|------|------|------|
| deepseek-chat | DeepSeek Chat | 通用对话模型 | 基础聊天 |
| deepseek-reasoner | DeepSeek-R1-0528 | 推理模型 | 深度思考 |

### 豆包模型
| 模型ID | 名称 | 描述 | 特性 |
|--------|------|------|------|
| doubao-seed-1-6-250615 | 豆包 Seed 1.6 | 基础对话模型 | 基础聊天 |
| doubao-1.5-vision-pro-250328 | 豆包 1.5 Vision Pro | 视觉理解模型 | 图像分析 |
| doubao-seed-1-6-thinking-250715 | 豆包 Seed 1.6 Thinking | 思考模型 | 深度推理 |

## API使用示例

### 1. DeepSeek聊天
```bash
curl -X POST "https://your-domain.com/api/gateway.php/deepseek/chat" \
  -H "Authorization: Bearer sk-xxxxxxxxxxxxxxxx" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "deepseek-chat",
    "messages": [{"role": "user", "content": "你好"}],
    "max_tokens": 4096,
    "temperature": 0.7
  }'
```

### 2. 豆包聊天
```bash
curl -X POST "https://your-domain.com/api/gateway.php/doubao/chat" \
  -H "Authorization: Bearer your-doubao-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "doubao-seed-1-6-250615",
    "messages": [{"role": "user", "content": "你好"}],
    "max_tokens": 4096,
    "temperature": 0.7
  }'
```

### 3. 统一接口
```bash
curl -X POST "https://your-domain.com/api/gateway.php/ai/chat" \
  -H "Content-Type: application/json" \
  -d '{
    "service": "deepseek",
    "model": "deepseek-chat",
    "messages": [{"role": "user", "content": "Hello"}]
  }'
```

## 安装和配置

### 1. 数据库安装
```bash
# 访问安装脚本
https://your-domain.com/api/install_ai_services.php

# 或检查表状态
https://your-domain.com/api/install_ai_services.php?action=check
```

### 2. 配置API密钥
通过网站后台AI客服设置页面配置DeepSeek和豆包的API密钥。

### 3. 测试接口
访问测试页面进行功能验证：
```
https://your-domain.com/api/ai_api_test.html
```

## 安全特性

### 1. 认证机制
- ✅ Bearer Token认证
- ✅ API密钥验证
- ✅ 请求头验证

### 2. 数据安全
- ✅ SQL注入防护
- ✅ XSS攻击防护
- ✅ 敏感信息掩码

### 3. 错误处理
- ✅ 统一错误响应格式
- ✅ 详细错误信息
- ✅ 错误日志记录

## 监控和日志

### 1. 使用日志
- API调用记录
- 响应时间统计
- 错误信息追踪

### 2. 状态监控
- 服务可用性检查
- 响应时间监控
- 自动状态更新

### 3. 对话历史
- 完整对话记录
- Token使用统计
- 会话管理

## 扩展性

### 1. 新增AI服务
- 模块化设计，易于添加新的AI服务
- 统一的接口规范
- 配置化模型管理

### 2. 功能扩展
- 支持流式响应
- 支持文件上传
- 支持批量处理

### 3. 性能优化
- 连接池管理
- 缓存机制
- 负载均衡

## 文件结构

```
网站后台/api/
├── deepseek_api.php          # DeepSeek API接口
├── doubao_api.php            # 豆包API接口
├── ai_service_api.php        # 统一AI服务管理
├── gateway.php               # API网关（已更新）
├── api_base.php              # API基础类
├── config.php                # 配置管理
├── install_ai_services.php   # 数据库安装脚本
├── ai_api_test.html          # API测试页面
└── AI_API_Documentation.md   # API文档

网站后台/sql/
└── ai_services_tables.sql    # 数据库表结构

网站后台/
└── AI服务API接口实现说明.md  # 本说明文档
```

## 总结

✅ **完成的功能**:
- DeepSeek和豆包的独立API接口
- 统一的AI服务管理接口
- 完整的数据库支持
- 详细的文档和测试工具
- 安全的认证和错误处理

✅ **技术特点**:
- RESTful API设计
- 模块化架构
- 统一错误处理
- 完整的日志记录
- 响应式测试界面

✅ **使用便利性**:
- 简单的API调用
- 详细的文档说明
- 可视化测试工具
- 自动化安装脚本

通过这些API接口，外部应用程序可以轻松集成和使用DeepSeek和豆包的AI功能，实现智能对话、内容生成等应用场景。
