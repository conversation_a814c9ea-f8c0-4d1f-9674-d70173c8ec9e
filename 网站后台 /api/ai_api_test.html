<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI API接口测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .test-section {
            margin-bottom: 40px;
            padding: 25px;
            border: 2px solid #e1e5e9;
            border-radius: 15px;
            background: #f8f9fa;
        }

        .test-section h2 {
            color: #495057;
            margin-bottom: 20px;
            font-size: 1.5em;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #495057;
        }

        input, select, textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        textarea {
            resize: vertical;
            min-height: 100px;
        }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: transform 0.2s, box-shadow 0.2s;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .response-area {
            margin-top: 20px;
            padding: 15px;
            background: #fff;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            min-height: 100px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            overflow-x: auto;
        }

        .success {
            border-left: 4px solid #28a745;
            background-color: #d4edda;
        }

        .error {
            border-left: 4px solid #dc3545;
            background-color: #f8d7da;
        }

        .loading {
            border-left: 4px solid #ffc107;
            background-color: #fff3cd;
        }

        .tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 2px solid #dee2e6;
        }

        .tab {
            padding: 12px 24px;
            cursor: pointer;
            border: none;
            background: none;
            font-size: 16px;
            font-weight: 600;
            color: #6c757d;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
        }

        .tab.active {
            color: #667eea;
            border-bottom-color: #667eea;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 20px;
            }
            
            h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 AI API接口测试</h1>
        
        <div class="tabs">
            <button class="tab active" onclick="switchTab('deepseek')">DeepSeek API</button>
            <button class="tab" onclick="switchTab('doubao')">豆包 API</button>
            <button class="tab" onclick="switchTab('unified')">统一接口</button>
        </div>

        <!-- DeepSeek API测试 -->
        <div id="deepseek" class="tab-content active">
            <div class="test-section">
                <h2>🧠 DeepSeek API测试</h2>
                
                <div class="form-group">
                    <label for="deepseek-api-key">API密钥:</label>
                    <input type="password" id="deepseek-api-key" placeholder="sk-xxxxxxxxxxxxxxxx">
                </div>

                <div class="grid">
                    <div>
                        <button class="btn" onclick="testDeepSeekModels()">获取模型列表</button>
                        <button class="btn" onclick="testDeepSeekStatus()">检查服务状态</button>
                        <button class="btn" onclick="validateDeepSeekKey()">验证API密钥</button>
                    </div>
                    <div>
                        <div class="form-group">
                            <label for="deepseek-model">模型:</label>
                            <select id="deepseek-model">
                                <option value="deepseek-chat">DeepSeek Chat</option>
                                <option value="deepseek-reasoner">DeepSeek-R1-0528</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="deepseek-message">消息内容:</label>
                    <textarea id="deepseek-message" placeholder="请输入要发送的消息...">你好，请介绍一下自己</textarea>
                </div>

                <button class="btn" onclick="testDeepSeekChat()">发送聊天消息</button>

                <div id="deepseek-response" class="response-area">等待测试结果...</div>
            </div>
        </div>

        <!-- 豆包API测试 -->
        <div id="doubao" class="tab-content">
            <div class="test-section">
                <h2>🫘 豆包API测试</h2>
                
                <div class="form-group">
                    <label for="doubao-api-key">API密钥:</label>
                    <input type="password" id="doubao-api-key" placeholder="豆包API密钥">
                </div>

                <div class="grid">
                    <div>
                        <button class="btn" onclick="testDoubaoModels()">获取模型列表</button>
                        <button class="btn" onclick="testDoubaoStatus()">检查服务状态</button>
                        <button class="btn" onclick="validateDoubaoKey()">验证API密钥</button>
                    </div>
                    <div>
                        <div class="form-group">
                            <label for="doubao-model">模型:</label>
                            <select id="doubao-model">
                                <option value="doubao-seed-1-6-250615">豆包 Seed 1.6</option>
                                <option value="doubao-1.5-vision-pro-250328">豆包 1.5 Vision Pro</option>
                                <option value="doubao-seed-1-6-thinking-250715">豆包 Seed 1.6 Thinking</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="doubao-message">消息内容:</label>
                    <textarea id="doubao-message" placeholder="请输入要发送的消息...">你好，请介绍一下自己</textarea>
                </div>

                <button class="btn" onclick="testDoubaoChat()">发送聊天消息</button>

                <div id="doubao-response" class="response-area">等待测试结果...</div>
            </div>
        </div>

        <!-- 统一接口测试 -->
        <div id="unified" class="tab-content">
            <div class="test-section">
                <h2>🔗 统一AI服务接口测试</h2>
                
                <div class="grid">
                    <div>
                        <button class="btn" onclick="testUnifiedServices()">获取服务列表</button>
                        <button class="btn" onclick="testUnifiedStatus()">获取所有状态</button>
                    </div>
                    <div>
                        <div class="form-group">
                            <label for="unified-service">选择服务:</label>
                            <select id="unified-service">
                                <option value="deepseek">DeepSeek</option>
                                <option value="doubao">豆包</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div id="unified-response" class="response-area">等待测试结果...</div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = './gateway.php';

        function switchTab(tabName) {
            // 隐藏所有标签内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除所有标签的活动状态
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 显示选中的标签内容
            document.getElementById(tabName).classList.add('active');
            
            // 激活选中的标签
            event.target.classList.add('active');
        }

        async function makeRequest(endpoint, options = {}) {
            try {
                const response = await fetch(`${API_BASE}${endpoint}`, {
                    method: options.method || 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    body: options.body ? JSON.stringify(options.body) : undefined
                });

                const data = await response.json();
                return { success: response.ok, data, status: response.status };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function displayResponse(elementId, result) {
            const element = document.getElementById(elementId);
            element.className = 'response-area';
            
            if (result.success) {
                element.classList.add('success');
                element.textContent = JSON.stringify(result.data, null, 2);
            } else {
                element.classList.add('error');
                element.textContent = `错误: ${result.error || result.data?.message || '未知错误'}`;
            }
        }

        function setLoading(elementId, message = '请求中...') {
            const element = document.getElementById(elementId);
            element.className = 'response-area loading';
            element.textContent = message;
        }

        // DeepSeek API测试函数
        async function testDeepSeekModels() {
            setLoading('deepseek-response', '获取DeepSeek模型列表...');
            const result = await makeRequest('/deepseek/models');
            displayResponse('deepseek-response', result);
        }

        async function testDeepSeekStatus() {
            setLoading('deepseek-response', '检查DeepSeek服务状态...');
            const result = await makeRequest('/deepseek/status');
            displayResponse('deepseek-response', result);
        }

        async function validateDeepSeekKey() {
            const apiKey = document.getElementById('deepseek-api-key').value;
            if (!apiKey) {
                alert('请输入API密钥');
                return;
            }

            setLoading('deepseek-response', '验证DeepSeek API密钥...');
            const result = await makeRequest('/deepseek/validate', {
                method: 'POST',
                body: { api_key: apiKey }
            });
            displayResponse('deepseek-response', result);
        }

        async function testDeepSeekChat() {
            const apiKey = document.getElementById('deepseek-api-key').value;
            const model = document.getElementById('deepseek-model').value;
            const message = document.getElementById('deepseek-message').value;

            if (!apiKey) {
                alert('请输入API密钥');
                return;
            }

            if (!message) {
                alert('请输入消息内容');
                return;
            }

            setLoading('deepseek-response', '发送DeepSeek聊天请求...');
            const result = await makeRequest('/deepseek/chat', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${apiKey}`
                },
                body: {
                    model: model,
                    messages: [{ role: 'user', content: message }],
                    max_tokens: 4096,
                    temperature: 0.7
                }
            });
            displayResponse('deepseek-response', result);
        }

        // 豆包API测试函数
        async function testDoubaoModels() {
            setLoading('doubao-response', '获取豆包模型列表...');
            const result = await makeRequest('/doubao/models');
            displayResponse('doubao-response', result);
        }

        async function testDoubaoStatus() {
            setLoading('doubao-response', '检查豆包服务状态...');
            const result = await makeRequest('/doubao/status');
            displayResponse('doubao-response', result);
        }

        async function validateDoubaoKey() {
            const apiKey = document.getElementById('doubao-api-key').value;
            if (!apiKey) {
                alert('请输入API密钥');
                return;
            }

            setLoading('doubao-response', '验证豆包API密钥...');
            const result = await makeRequest('/doubao/validate', {
                method: 'POST',
                body: { api_key: apiKey }
            });
            displayResponse('doubao-response', result);
        }

        async function testDoubaoChat() {
            const apiKey = document.getElementById('doubao-api-key').value;
            const model = document.getElementById('doubao-model').value;
            const message = document.getElementById('doubao-message').value;

            if (!apiKey) {
                alert('请输入API密钥');
                return;
            }

            if (!message) {
                alert('请输入消息内容');
                return;
            }

            setLoading('doubao-response', '发送豆包聊天请求...');
            const result = await makeRequest('/doubao/chat', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${apiKey}`
                },
                body: {
                    model: model,
                    messages: [{ role: 'user', content: message }],
                    max_tokens: 4096,
                    temperature: 0.7
                }
            });
            displayResponse('doubao-response', result);
        }

        // 统一接口测试函数
        async function testUnifiedServices() {
            setLoading('unified-response', '获取支持的AI服务列表...');
            const result = await makeRequest('/ai/services');
            displayResponse('unified-response', result);
        }

        async function testUnifiedStatus() {
            setLoading('unified-response', '获取所有AI服务状态...');
            const result = await makeRequest('/ai/status');
            displayResponse('unified-response', result);
        }
    </script>
</body>
</html>
