<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化AI API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .test-section h2 {
            color: #555;
            margin-bottom: 15px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        textarea {
            height: 80px;
            resize: vertical;
        }
        .btn {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .btn:hover {
            background: #005a87;
        }
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .response {
            margin-top: 15px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .loading {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 简化AI API测试</h1>
        
        <div class="grid">
            <!-- DeepSeek测试 -->
            <div class="test-section">
                <h2>🧠 DeepSeek API测试</h2>
                
                <div class="form-group">
                    <label for="deepseek-key">API密钥:</label>
                    <input type="password" id="deepseek-key" placeholder="sk-xxxxxxxxxxxxxxxx">
                </div>
                
                <div class="form-group">
                    <label for="deepseek-model">模型:</label>
                    <select id="deepseek-model">
                        <option value="deepseek-chat">DeepSeek Chat</option>
                        <option value="deepseek-reasoner">DeepSeek-R1-0528</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="deepseek-message">消息:</label>
                    <textarea id="deepseek-message" placeholder="输入消息...">你好，请介绍一下自己</textarea>
                </div>
                
                <button class="btn" onclick="testDeepSeek('models')">获取模型</button>
                <button class="btn" onclick="testDeepSeek('status')">检查状态</button>
                <button class="btn" onclick="testDeepSeek('validate')">验证密钥</button>
                <button class="btn" onclick="testDeepSeek('chat')">发送消息</button>
                
                <div id="deepseek-response" class="response">等待测试...</div>
            </div>

            <!-- 豆包测试 -->
            <div class="test-section">
                <h2>🫘 豆包API测试</h2>
                
                <div class="form-group">
                    <label for="doubao-key">API密钥:</label>
                    <input type="password" id="doubao-key" placeholder="豆包API密钥">
                </div>
                
                <div class="form-group">
                    <label for="doubao-model">模型:</label>
                    <select id="doubao-model">
                        <option value="doubao-seed-1-6-250615">豆包 Seed 1.6</option>
                        <option value="doubao-1.5-vision-pro-250328">豆包 1.5 Vision Pro</option>
                        <option value="doubao-seed-1-6-thinking-250715">豆包 Seed 1.6 Thinking</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="doubao-message">消息:</label>
                    <textarea id="doubao-message" placeholder="输入消息...">你好，请介绍一下自己</textarea>
                </div>
                
                <button class="btn" onclick="testDoubao('models')">获取模型</button>
                <button class="btn" onclick="testDoubao('status')">检查状态</button>
                <button class="btn" onclick="testDoubao('validate')">验证密钥</button>
                <button class="btn" onclick="testDoubao('chat')">发送消息</button>
                
                <div id="doubao-response" class="response">等待测试...</div>
            </div>
        </div>
    </div>

    <script>
        async function makeRequest(url, options = {}) {
            try {
                const response = await fetch(url, {
                    method: options.method || 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    body: options.body ? JSON.stringify(options.body) : undefined
                });

                const data = await response.json();
                return { success: response.ok, data, status: response.status };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function displayResponse(elementId, result) {
            const element = document.getElementById(elementId);
            element.className = 'response';
            
            if (result.success) {
                element.classList.add('success');
                element.textContent = JSON.stringify(result.data, null, 2);
            } else {
                element.classList.add('error');
                element.textContent = `错误: ${result.error || result.data?.message || '未知错误'}`;
            }
        }

        function setLoading(elementId, message = '请求中...') {
            const element = document.getElementById(elementId);
            element.className = 'response loading';
            element.textContent = message;
        }

        async function testDeepSeek(action) {
            const apiKey = document.getElementById('deepseek-key').value;
            const model = document.getElementById('deepseek-model').value;
            const message = document.getElementById('deepseek-message').value;
            
            setLoading('deepseek-response', `测试DeepSeek ${action}...`);
            
            let result;
            
            switch (action) {
                case 'models':
                    result = await makeRequest('./simple_deepseek_api.php/models');
                    break;
                    
                case 'status':
                    result = await makeRequest('./simple_deepseek_api.php/status');
                    break;
                    
                case 'validate':
                    if (!apiKey) {
                        alert('请输入API密钥');
                        return;
                    }
                    result = await makeRequest('./simple_deepseek_api.php/validate', {
                        method: 'POST',
                        body: { api_key: apiKey }
                    });
                    break;
                    
                case 'chat':
                    if (!apiKey) {
                        alert('请输入API密钥');
                        return;
                    }
                    if (!message) {
                        alert('请输入消息');
                        return;
                    }
                    result = await makeRequest('./simple_deepseek_api.php/chat', {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${apiKey}`
                        },
                        body: {
                            model: model,
                            messages: [{ role: 'user', content: message }],
                            max_tokens: 4096,
                            temperature: 0.7
                        }
                    });
                    break;
            }
            
            displayResponse('deepseek-response', result);
        }

        async function testDoubao(action) {
            const apiKey = document.getElementById('doubao-key').value;
            const model = document.getElementById('doubao-model').value;
            const message = document.getElementById('doubao-message').value;
            
            setLoading('doubao-response', `测试豆包 ${action}...`);
            
            let result;
            
            switch (action) {
                case 'models':
                    result = await makeRequest('./simple_doubao_api.php/models');
                    break;
                    
                case 'status':
                    result = await makeRequest('./simple_doubao_api.php/status');
                    break;
                    
                case 'validate':
                    if (!apiKey) {
                        alert('请输入API密钥');
                        return;
                    }
                    result = await makeRequest('./simple_doubao_api.php/validate', {
                        method: 'POST',
                        body: { api_key: apiKey }
                    });
                    break;
                    
                case 'chat':
                    if (!apiKey) {
                        alert('请输入API密钥');
                        return;
                    }
                    if (!message) {
                        alert('请输入消息');
                        return;
                    }
                    result = await makeRequest('./simple_doubao_api.php/chat', {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${apiKey}`
                        },
                        body: {
                            model: model,
                            messages: [{ role: 'user', content: message }],
                            max_tokens: 4096,
                            temperature: 0.7
                        }
                    });
                    break;
            }
            
            displayResponse('doubao-response', result);
        }
    </script>
</body>
</html>
