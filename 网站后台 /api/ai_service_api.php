<?php
/**
 * AI服务管理API接口
 * 统一管理DeepSeek和豆包AI服务的配置和状态
 * 
 * @version 1.0.0
 */

// 防止直接访问
if (!defined('API_ACCESS')) {
    define('API_ACCESS', true);
}

// 加载必要的文件
require_once __DIR__ . '/api_base.php';
require_once __DIR__ . '/deepseek_api.php';
require_once __DIR__ . '/doubao_api.php';

class AiServiceApi extends ApiBase {
    private $supportedServices = ['deepseek', 'doubao'];
    
    /**
     * 处理请求
     */
    public function handleRequest() {
        // 解析请求路径
        $path = $_SERVER['PATH_INFO'] ?? '';
        $path = trim($path, '/');
        
        // 分割路径获取端点
        $segments = explode('/', $path);
        $endpoint = $segments[1] ?? ''; // segments[0] 是 'ai'
        
        // 路由处理
        switch ($endpoint) {
            case 'services':
                return $this->handleServices();
            case 'config':
                return $this->handleConfig();
            case 'status':
                return $this->handleStatus();
            case 'validate':
                return $this->handleValidate();
            case 'chat':
                return $this->handleChat();
            default:
                return $this->respondError('Invalid endpoint', 404);
        }
    }

    /**
     * 获取支持的AI服务列表
     */
    private function handleServices() {
        if ($this->method !== 'GET') {
            return $this->respondError('Method not allowed', 405);
        }

        $services = [
            'deepseek' => [
                'name' => 'DeepSeek',
                'description' => 'DeepSeek AI服务',
                'api_url' => 'https://api.deepseek.com',
                'models' => ['deepseek-chat', 'deepseek-reasoner'],
                'features' => ['chat', 'reasoning']
            ],
            'doubao' => [
                'name' => '豆包',
                'description' => '豆包AI服务',
                'api_url' => 'https://ark.cn-beijing.volces.com/api/v3',
                'models' => ['doubao-seed-1-6-250615', 'doubao-1.5-vision-pro-250328', 'doubao-seed-1-6-thinking-250715'],
                'features' => ['chat', 'vision', 'thinking']
            ]
        ];

        return $this->respondSuccess([
            'services' => $services,
            'total' => count($services)
        ], 'Services retrieved successfully');
    }

    /**
     * 管理AI服务配置
     */
    private function handleConfig() {
        $service = $this->request['service'] ?? '';
        
        if (!in_array($service, $this->supportedServices)) {
            return $this->respondError('Unsupported service', 400);
        }

        switch ($this->method) {
            case 'GET':
                return $this->getConfig($service);
            case 'POST':
                return $this->saveConfig($service);
            case 'PUT':
                return $this->updateConfig($service);
            case 'DELETE':
                return $this->deleteConfig($service);
            default:
                return $this->respondError('Method not allowed', 405);
        }
    }

    /**
     * 获取AI服务状态
     */
    private function handleStatus() {
        if ($this->method !== 'GET') {
            return $this->respondError('Method not allowed', 405);
        }

        $service = $this->request['service'] ?? '';
        
        if ($service && !in_array($service, $this->supportedServices)) {
            return $this->respondError('Unsupported service', 400);
        }

        if ($service) {
            // 获取单个服务状态
            return $this->getServiceStatus($service);
        } else {
            // 获取所有服务状态
            return $this->getAllServicesStatus();
        }
    }

    /**
     * 验证API密钥
     */
    private function handleValidate() {
        if ($this->method !== 'POST') {
            return $this->respondError('Method not allowed', 405);
        }

        $service = $this->request['service'] ?? '';
        $apiKey = $this->request['api_key'] ?? '';

        if (!in_array($service, $this->supportedServices)) {
            return $this->respondError('Unsupported service', 400);
        }

        if (empty($apiKey)) {
            return $this->respondError('Missing API key', 400);
        }

        try {
            if ($service === 'deepseek') {
                $deepseekApi = new DeepSeekApi();
                $validation = $deepseekApi->validateApiKey($apiKey);
            } else if ($service === 'doubao') {
                $doubaoApi = new DoubaoApi();
                $validation = $doubaoApi->validateApiKey($apiKey);
            }

            if ($validation['valid']) {
                return $this->respondSuccess([
                    'service' => $service,
                    'valid' => true
                ], 'API key is valid');
            } else {
                return $this->respondError($validation['error'], 401);
            }

        } catch (Exception $e) {
            return $this->respondError('Validation failed: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 统一聊天接口
     */
    private function handleChat() {
        if ($this->method !== 'POST') {
            return $this->respondError('Method not allowed', 405);
        }

        $service = $this->request['service'] ?? '';
        
        if (!in_array($service, $this->supportedServices)) {
            return $this->respondError('Unsupported service', 400);
        }

        try {
            if ($service === 'deepseek') {
                $api = new DeepSeekApi();
                // 模拟chat端点请求
                $_SERVER['PATH_INFO'] = '/deepseek/chat';
                return $api->handleRequest();
            } else if ($service === 'doubao') {
                $api = new DoubaoApi();
                // 模拟chat端点请求
                $_SERVER['PATH_INFO'] = '/doubao/chat';
                return $api->handleRequest();
            }

        } catch (Exception $e) {
            return $this->respondError('Chat request failed: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取服务配置
     */
    private function getConfig($service) {
        try {
            $stmt = $this->db->prepare("
                SELECT * FROM ai_service_configs 
                WHERE service = ? AND user_id = ?
            ");
            $stmt->execute([$service, $this->getCurrentUserId()]);
            $config = $stmt->fetch();

            if ($config) {
                // 隐藏敏感信息
                $config['api_keys'] = $this->maskApiKeys($config['api_keys'] ?? '');
                unset($config['user_id']);
                
                return $this->respondSuccess($config, 'Config retrieved successfully');
            } else {
                return $this->respondSuccess([
                    'service' => $service,
                    'enabled' => false,
                    'api_keys' => [],
                    'model' => $this->getDefaultModel($service),
                    'settings' => []
                ], 'Default config returned');
            }

        } catch (PDOException $e) {
            return $this->respondError('Database error', 500);
        }
    }

    /**
     * 保存服务配置
     */
    private function saveConfig($service) {
        $enabled = $this->request['enabled'] ?? false;
        $apiKeys = $this->request['api_keys'] ?? [];
        $model = $this->request['model'] ?? $this->getDefaultModel($service);
        $settings = $this->request['settings'] ?? [];

        try {
            $stmt = $this->db->prepare("
                INSERT INTO ai_service_configs 
                (service, user_id, enabled, api_keys, model, settings, created_at, updated_at) 
                VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
                ON DUPLICATE KEY UPDATE 
                enabled = VALUES(enabled),
                api_keys = VALUES(api_keys),
                model = VALUES(model),
                settings = VALUES(settings),
                updated_at = NOW()
            ");

            $stmt->execute([
                $service,
                $this->getCurrentUserId(),
                $enabled ? 1 : 0,
                json_encode($apiKeys),
                $model,
                json_encode($settings)
            ]);

            return $this->respondSuccess([
                'service' => $service,
                'saved' => true
            ], 'Config saved successfully');

        } catch (PDOException $e) {
            return $this->respondError('Failed to save config', 500);
        }
    }

    /**
     * 更新服务配置
     */
    private function updateConfig($service) {
        return $this->saveConfig($service); // 使用相同的逻辑
    }

    /**
     * 删除服务配置
     */
    private function deleteConfig($service) {
        try {
            $stmt = $this->db->prepare("
                DELETE FROM ai_service_configs 
                WHERE service = ? AND user_id = ?
            ");
            $stmt->execute([$service, $this->getCurrentUserId()]);

            return $this->respondSuccess([
                'service' => $service,
                'deleted' => true
            ], 'Config deleted successfully');

        } catch (PDOException $e) {
            return $this->respondError('Failed to delete config', 500);
        }
    }

    /**
     * 获取单个服务状态
     */
    private function getServiceStatus($service) {
        try {
            if ($service === 'deepseek') {
                $api = new DeepSeekApi();
                $status = $api->checkDeepSeekStatus();
            } else if ($service === 'doubao') {
                $api = new DoubaoApi();
                $status = $api->checkDoubaoStatus();
            }

            return $this->respondSuccess([
                'service' => $service,
                'status' => $status['status'],
                'response_time' => $status['response_time'],
                'last_check' => date('Y-m-d H:i:s')
            ], 'Status retrieved successfully');

        } catch (Exception $e) {
            return $this->respondError('Status check failed: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取所有服务状态
     */
    private function getAllServicesStatus() {
        $statuses = [];

        foreach ($this->supportedServices as $service) {
            try {
                if ($service === 'deepseek') {
                    $api = new DeepSeekApi();
                    $status = $api->checkDeepSeekStatus();
                } else if ($service === 'doubao') {
                    $api = new DoubaoApi();
                    $status = $api->checkDoubaoStatus();
                }

                $statuses[$service] = [
                    'status' => $status['status'],
                    'response_time' => $status['response_time']
                ];

            } catch (Exception $e) {
                $statuses[$service] = [
                    'status' => 'error',
                    'error' => $e->getMessage()
                ];
            }
        }

        return $this->respondSuccess([
            'services' => $statuses,
            'last_check' => date('Y-m-d H:i:s')
        ], 'All statuses retrieved successfully');
    }

    /**
     * 获取当前用户ID
     */
    private function getCurrentUserId() {
        // 这里应该从认证系统获取用户ID
        // 暂时返回默认值
        return 1;
    }

    /**
     * 掩码API密钥
     */
    private function maskApiKeys($apiKeysJson) {
        $apiKeys = json_decode($apiKeysJson, true) ?: [];
        $masked = [];

        foreach ($apiKeys as $key) {
            $masked[] = substr($key, 0, 8) . '...';
        }

        return $masked;
    }

    /**
     * 获取默认模型
     */
    private function getDefaultModel($service) {
        $defaults = [
            'deepseek' => 'deepseek-chat',
            'doubao' => 'doubao-seed-1-6-250615'
        ];

        return $defaults[$service] ?? '';
    }
}

// 如果直接访问此文件，处理请求
if (basename($_SERVER['SCRIPT_NAME']) === 'ai_service_api.php') {
    $api = new AiServiceApi();
    $api->handleRequest();
}
