<?php
/**
 * AI服务数据库安装脚本
 * 创建AI服务所需的数据库表
 * 
 * @version 1.0.0
 */

// 防止直接访问
if (!defined('API_ACCESS')) {
    define('API_ACCESS', true);
}

// 加载配置
require_once __DIR__ . '/config.php';

class AiServicesInstaller {
    private $db;
    private $config;

    public function __construct() {
        $this->config = new ApiConfig();
        $this->connectDatabase();
    }

    /**
     * 连接数据库
     */
    private function connectDatabase() {
        try {
            $dbConfig = $this->config->getDatabaseConfig();
            $dsn = "mysql:host={$dbConfig['host']};dbname={$dbConfig['dbname']};charset=utf8mb4";
            $this->db = new PDO($dsn, $dbConfig['user'], $dbConfig['pass'], [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false
            ]);
        } catch (PDOException $e) {
            die("数据库连接失败: " . $e->getMessage());
        }
    }

    /**
     * 安装AI服务表
     */
    public function install() {
        $tables = [
            'ai_service_configs' => $this->getAiServiceConfigsTableSql(),
            'ai_api_usage_logs' => $this->getAiApiUsageLogsTableSql(),
            'ai_service_status' => $this->getAiServiceStatusTableSql(),
            'ai_chat_history' => $this->getAiChatHistoryTableSql()
        ];

        $results = [];

        foreach ($tables as $tableName => $sql) {
            try {
                $this->db->exec($sql);
                $results[$tableName] = '创建成功';
            } catch (PDOException $e) {
                $results[$tableName] = '创建失败: ' . $e->getMessage();
            }
        }

        // 插入默认配置
        $this->insertDefaultConfigs();

        return $results;
    }

    /**
     * AI服务配置表SQL
     */
    private function getAiServiceConfigsTableSql() {
        return "
            CREATE TABLE IF NOT EXISTS `ai_service_configs` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `service` varchar(50) NOT NULL COMMENT 'AI服务名称 (deepseek, doubao)',
              `user_id` int(11) NOT NULL DEFAULT 1 COMMENT '用户ID',
              `enabled` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否启用',
              `api_keys` text COMMENT 'API密钥列表 (JSON格式)',
              `model` varchar(100) DEFAULT NULL COMMENT '当前使用的模型',
              `settings` text COMMENT '其他设置 (JSON格式)',
              `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
              `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
              PRIMARY KEY (`id`),
              UNIQUE KEY `unique_service_user` (`service`, `user_id`),
              KEY `idx_service` (`service`),
              KEY `idx_user_id` (`user_id`),
              KEY `idx_enabled` (`enabled`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI服务配置表'
        ";
    }

    /**
     * AI API使用日志表SQL
     */
    private function getAiApiUsageLogsTableSql() {
        return "
            CREATE TABLE IF NOT EXISTS `ai_api_usage_logs` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `service` varchar(50) NOT NULL COMMENT 'AI服务名称',
              `action` varchar(50) NOT NULL COMMENT '操作类型 (chat, validate, status)',
              `model` varchar(100) DEFAULT NULL COMMENT '使用的模型',
              `api_key_masked` varchar(20) DEFAULT NULL COMMENT '掩码后的API密钥',
              `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
              `user_agent` text COMMENT '用户代理',
              `request_data` text COMMENT '请求数据 (JSON格式)',
              `response_status` varchar(20) DEFAULT 'success' COMMENT '响应状态',
              `response_time` int(11) DEFAULT NULL COMMENT '响应时间(毫秒)',
              `error_message` text COMMENT '错误信息',
              `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
              PRIMARY KEY (`id`),
              KEY `idx_service` (`service`),
              KEY `idx_action` (`action`),
              KEY `idx_created_at` (`created_at`),
              KEY `idx_response_status` (`response_status`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI API使用日志表'
        ";
    }

    /**
     * AI服务状态监控表SQL
     */
    private function getAiServiceStatusTableSql() {
        return "
            CREATE TABLE IF NOT EXISTS `ai_service_status` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `service` varchar(50) NOT NULL COMMENT 'AI服务名称',
              `status` varchar(20) NOT NULL COMMENT '服务状态 (online, offline, error)',
              `response_time` int(11) DEFAULT NULL COMMENT '响应时间(毫秒)',
              `error_message` text COMMENT '错误信息',
              `check_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
              PRIMARY KEY (`id`),
              KEY `idx_service` (`service`),
              KEY `idx_status` (`status`),
              KEY `idx_check_time` (`check_time`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI服务状态监控表'
        ";
    }

    /**
     * AI对话历史表SQL
     */
    private function getAiChatHistoryTableSql() {
        return "
            CREATE TABLE IF NOT EXISTS `ai_chat_history` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `service` varchar(50) NOT NULL COMMENT 'AI服务名称',
              `model` varchar(100) NOT NULL COMMENT '使用的模型',
              `session_id` varchar(100) DEFAULT NULL COMMENT '会话ID',
              `user_message` text NOT NULL COMMENT '用户消息',
              `ai_response` text COMMENT 'AI回复',
              `tokens_used` int(11) DEFAULT NULL COMMENT '使用的token数量',
              `response_time` int(11) DEFAULT NULL COMMENT '响应时间(毫秒)',
              `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
              `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
              PRIMARY KEY (`id`),
              KEY `idx_service` (`service`),
              KEY `idx_model` (`model`),
              KEY `idx_session_id` (`session_id`),
              KEY `idx_created_at` (`created_at`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI对话历史表'
        ";
    }

    /**
     * 插入默认配置
     */
    private function insertDefaultConfigs() {
        try {
            $stmt = $this->db->prepare("
                INSERT IGNORE INTO `ai_service_configs` 
                (`service`, `user_id`, `enabled`, `api_keys`, `model`, `settings`) 
                VALUES (?, ?, ?, ?, ?, ?)
            ");

            // DeepSeek默认配置
            $stmt->execute([
                'deepseek',
                1,
                0,
                '[]',
                'deepseek-chat',
                json_encode([
                    'temperature' => 0.7,
                    'max_tokens' => 4096,
                    'enable_reasoning' => false
                ])
            ]);

            // 豆包默认配置
            $stmt->execute([
                'doubao',
                1,
                0,
                '[]',
                'doubao-seed-1-6-250615',
                json_encode([
                    'temperature' => 0.7,
                    'max_tokens' => 4096,
                    'enable_thinking' => false
                ])
            ]);

        } catch (PDOException $e) {
            // 忽略插入错误（可能已存在）
        }
    }

    /**
     * 检查表是否存在
     */
    public function checkTables() {
        $tables = ['ai_service_configs', 'ai_api_usage_logs', 'ai_service_status', 'ai_chat_history'];
        $results = [];

        foreach ($tables as $table) {
            try {
                $stmt = $this->db->query("SHOW TABLES LIKE '$table'");
                $results[$table] = $stmt->rowCount() > 0 ? '存在' : '不存在';
            } catch (PDOException $e) {
                $results[$table] = '检查失败: ' . $e->getMessage();
            }
        }

        return $results;
    }
}

// 如果直接访问此文件，执行安装
if (basename($_SERVER['SCRIPT_NAME']) === 'install_ai_services.php') {
    header('Content-Type: application/json; charset=UTF-8');
    
    try {
        $installer = new AiServicesInstaller();
        
        $action = $_GET['action'] ?? 'install';
        
        if ($action === 'check') {
            $results = $installer->checkTables();
            echo json_encode([
                'success' => true,
                'action' => 'check',
                'results' => $results
            ]);
        } else {
            $results = $installer->install();
            echo json_encode([
                'success' => true,
                'action' => 'install',
                'results' => $results,
                'message' => 'AI服务数据库表安装完成'
            ]);
        }
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
}
